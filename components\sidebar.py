from dash import html, dcc

def get_sidebar():
    return html.Div(
        [
            html.Div(
                [
                    html.<PERSON><PERSON>(
                        html.I(className="fas fa-bars"),
                        id="sidebar-toggle-btn",
                        className="sidebar-toggle",
                    ),
                ],
                className="sidebar-header",
            ),
            html.Div(
                [
                    dcc.Link(
                        [
                            html.I(className="fas fa-home"),
                            html.Span("🚨🚨🚨 NEW SIDEBAR TEST 🚨🚨🚨"),
                        ],
                        href="/",
                        className="sidebar-nav-item",
                    ),
                    dcc.Link(
                        [
                            html.I(className="fas fa-building"),
                            html.Span("Project & Business Analysis"),
                        ],
                        href="/projects",
                        className="sidebar-nav-item",
                    ),
                    dcc.<PERSON>(
                        [
                            html.I(className="fas fa-shield-alt"),
                            html.Span("User & Security Audit"),
                        ],
                        href="/users",
                        className="sidebar-nav-item",
                    ),
                    dcc.Link(
                        [
                            html.I(className="fas fa-cogs"),
                            html.Span("Configuration & Fields"),
                        ],
                        href="/configuration",
                        className="sidebar-nav-item",
                    ),
                    dcc.<PERSON>(
                        [
                            html.I(className="fas fa-file-alt"),
                            html.Span("Issues & Content"),
                        ],
                        href="/tickets",
                        className="sidebar-nav-item",
                    ),
                    dcc.Link(
                        [
                            html.I(className="fas fa-project-diagram"),
                            html.Span("Workflow & Automation"),
                        ],
                        href="/workflows",
                        className="sidebar-nav-item",
                    ),
                    dcc.Link(
                        [
                            html.I(className="fas fa-chart-line"),
                            html.Span("Agile & Boards"),
                        ],
                        href="/agile",
                        className="sidebar-nav-item",
                    ),
                    dcc.Link(
                        [
                            html.I(className="fas fa-robot"),
                            html.Span("AI Assistant"),
                        ],
                        href="/chatbot",
                        className="sidebar-nav-item",
                    ),

                ],
                className="sidebar-nav",
            ),
        ],
        id="sidebar",
        className="sidebar",
    )

def register_sidebar_callbacks(callback_manager):
    from dash import Output, Input, State

    @callback_manager.register(
        output=[
            Output("sidebar", "className"),
            Output("page-content", "className"),
            Output("header", "className")
        ],
        inputs=Input("sidebar-toggle-btn", "n_clicks"),
        state=[
            State("sidebar", "className"),
            State("page-content", "className"),
            State("header", "className")
        ],
        prevent_initial_call=True
    )
    def toggle_sidebar(n_clicks, sidebar_class, page_content_class, header_class):
        if n_clicks is None:
            return "sidebar", "page-content", "header"

        if "collapsed" in sidebar_class:
            return "sidebar", "page-content", "header"
        else:
            return "sidebar collapsed", "page-content collapsed", "header collapsed"