from dash import html
import dash_bootstrap_components as dbc
import logging
from components.cards import create_metric_card
from kpi_calculator import get_user_security_kpis

logger = logging.getLogger(__name__)

def layout():
    """
    USERS PAGE WITH KPI CARDS - NO UNWANTED SECTIONS
    """
    try:
        logger.info("👥 USERS PAGE WITH KPI CARDS LOADED!")

        # Get real KPI data from load schema
        try:
            kpi_data = get_user_security_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} User Security KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading User KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_users': 0, 'active_users': 0, 'inactive_users': 0, 'external_users': 0,
                'users_in_groups': 0, 'total_groups': 0, 'users_with_roles': 0, 'permission_schemes': 0,
                'scheme_permissions': 0, 'users_with_issues': 0, 'users_with_worklogs': 0, 'admin_users': 0
            }

        # 👥 USER & SECURITY AUDIT - 12 KPI CARDS (NO UNWANTED ONES)
        # User Metrics (6)
        user_metrics_cards = [
            create_metric_card("Total Users", kpi_data['total_users'], "All users", "#0079BF"),
            create_metric_card("Active Users", kpi_data['active_users'], "Currently active", "#61BD4F"),
            create_metric_card("Inactive Users", kpi_data['inactive_users'], "Inactive accounts", "#EB5A46"),
            create_metric_card("External Users", kpi_data['external_users'], "External accounts", "#F2D600"),
            create_metric_card("Users in Groups", kpi_data['users_in_groups'], "Group members", "#FFAB4A"),
            create_metric_card("Total Groups", kpi_data['total_groups'], "All groups", "#C377E0")
        ]

        # Security & Permissions (6)
        security_permissions_cards = [
            create_metric_card("Users with Roles", kpi_data['users_with_roles'], "Role assignments", "#00C7E6"),
            create_metric_card("Permission Schemes", kpi_data['permission_schemes'], "Permission setups", "#51E898"),
            create_metric_card("Scheme Permissions", kpi_data['scheme_permissions'], "Total permissions", "#FF78CB"),
            create_metric_card("Users with Issues", kpi_data['users_with_issues'], "Issue reporters", "#4ECDC4"),
            create_metric_card("Users with Worklogs", kpi_data['users_with_worklogs'], "Time loggers", "#0079BF"),
            create_metric_card("Admin Users", kpi_data['admin_users'], "Admin accounts", "#61BD4F")
        ]

        # Combine all 12 KPI cards
        all_kpi_cards = user_metrics_cards + security_permissions_cards

        return html.Div([
            dbc.Container([
                # Page Title
                html.Div([
                    html.H2("👥 Users & Security Analysis", className="page-title mb-3"),
                    html.P("User and security audit with KPI cards - NO unwanted sections",
                          className="page-subtitle mb-4")
                ]),

                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="users-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),

                # User Metrics Section (6 KPIs)
                html.Div([
                    html.H4("👥 User Metrics", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in user_metrics_cards], className="mb-4"),
                ]),

                # Security & Permissions Section (6 KPIs)
                html.Div([
                    html.H4("🔒 Security & Permissions", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in security_permissions_cards], className="mb-4"),
                ])

            ], fluid=True)
        ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh"})

    except Exception as e:
        logger.error(f"Error in users layout: {e}")
        return html.Div([
            dbc.Alert(f"Error: {str(e)}", color="danger"),
            html.P("Users layout failed to load.")
        ])
