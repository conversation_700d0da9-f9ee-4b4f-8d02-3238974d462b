from dash import html
import dash_bootstrap_components as dbc
import logging

logger = logging.getLogger(__name__)

def layout():
    """
    COMPLETELY CLEAN USERS PAGE - NO UNWANTED SECTIONS!
    """
    try:
        logger.info("👥 COMPLETELY CLEAN USERS PAGE LOADED!")
        
        return html.Div([
            dbc.Container([
                # MASSIVE DEBUG MESSAGE - IMPOSSIBLE TO MISS
                html.Div([
                    html.H1("🔥🔥🔥 USERS PAGE TEST 🔥🔥🔥",
                           style={"fontSize": "4rem", "color": "red", "textAlign": "center", "fontWeight": "bold"}),
                    html.H1("🔥🔥🔥 USERS PAGE TEST 🔥🔥🔥",
                           style={"fontSize": "4rem", "color": "red", "textAlign": "center", "fontWeight": "bold"}),
                    html.H1("🔥🔥🔥 USERS PAGE TEST 🔥🔥🔥",
                           style={"fontSize": "4rem", "color": "red", "textAlign": "center", "fontWeight": "bold"}),
                ], style={"backgroundColor": "yellow", "padding": "3rem", "border": "10px solid red", "margin": "2rem"}),
                
                # Simple clean content
                html.H2("👥 Users & Security Analysis", className="mb-4"),
                html.P("This is the clean users page. Your 3 KPI tables will be integrated here.", 
                      className="mb-4", style={"fontSize": "1.1rem"}),
                
                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="users-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),
                
                # Placeholder for your KPI tables
                html.Div([
                    html.H4("📋 Your KPI Tables", className="mb-4"),
                    
                    # Table refresh button
                    dbc.Button(
                        [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Tables")],
                        id="users-table-refresh",
                        color="secondary",
                        className="mb-4"
                    ),
                    
                    # Table placeholders
                    html.Div([
                        html.H5("1. User List", className="mb-2"),
                        html.Div("👥 User List table will load here", 
                                id="users-list-table-placeholder", 
                                className="mb-4 p-3 border rounded")
                    ]),
                    
                    html.Div([
                        html.H5("2. User Worklog Contribution", className="mb-2"),
                        html.Div("⏰ User Worklog Contribution table will load here", 
                                id="users-worklog-table-placeholder", 
                                className="mb-4 p-3 border rounded")
                    ]),
                    
                    html.Div([
                        html.H5("3. Active User Issue Creation Engagement", className="mb-2"),
                        html.Div("📝 Active User Issue Creation Engagement table will load here", 
                                id="users-issues-table-placeholder", 
                                className="mb-4 p-3 border rounded")
                    ])
                ])
                
            ], fluid=True)
        ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh"})
        
    except Exception as e:
        logger.error(f"Error in clean users layout: {e}")
        return html.Div([
            dbc.Alert(f"Error: {str(e)}", color="danger"),
            html.P("Clean layout failed to load.")
        ])
