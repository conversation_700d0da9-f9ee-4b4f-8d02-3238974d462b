from dash import html
import dash_bootstrap_components as dbc

def layout():
    """
    BRAND NEW TEST LAYOUT - IMPOSSIBLE TO MISS
    """
    return html.Div([
        html.H1("🚨🚨🚨 BRAND NEW USERS LAYOUT 🚨🚨🚨", 
               style={"color": "red", "fontSize": "4rem", "textAlign": "center"}),
        html.H1("🚨🚨🚨 BRAND NEW USERS LAYOUT 🚨🚨🚨", 
               style={"color": "red", "fontSize": "4rem", "textAlign": "center"}),
        html.H1("🚨🚨🚨 BRAND NEW USERS LAYOUT 🚨🚨🚨", 
               style={"color": "red", "fontSize": "4rem", "textAlign": "center"}),
        html.H2("If you see this, the new layout is working!", 
               style={"color": "green", "fontSize": "2rem", "textAlign": "center"}),
        html.H2("If you still see the old sections, there's a different file being loaded!", 
               style={"color": "blue", "fontSize": "1.5rem", "textAlign": "center"})
    ], style={"backgroundColor": "yellow", "padding": "2rem", "minHeight": "100vh"})
