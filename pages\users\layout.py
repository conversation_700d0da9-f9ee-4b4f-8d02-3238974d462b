from dash import html
import dash_bootstrap_components as dbc
import logging

logger = logging.getLogger(__name__)

def layout():
    """
    CLEAN USERS PAGE - NO UNWANTED SECTIONS
    """
    try:
        logger.info("👥 CLEAN USERS PAGE LOADED!")

        return html.Div([
            dbc.Container([
                # Page Title
                html.Div([
                    html.H2("👥 Users & Security Analysis", className="page-title mb-3"),
                    html.P("User analysis with your 3 KPI tables - NO unwanted sections",
                          className="page-subtitle mb-4")
                ]),

                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.<PERSON><PERSON>(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="users-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),

                # User Data Tables Section
                html.Div([
                    html.H4("📋 User Analysis Tables", className="section-title mb-3"),

                    # Table refresh button
                    dbc.<PERSON><PERSON>(
                        [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Tables")],
                        id="users-table-refresh",
                        color="secondary",
                        className="mb-4"
                    ),

                    # User List Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("👥 User List table will load here",
                                    id="users-list-table-placeholder",
                                    className="mb-4 p-3 border rounded")
                        ], width=12, className="mb-4")
                    ]),

                    # User Worklog Contribution Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("⏰ User Worklog Contribution table will load here",
                                    id="users-worklog-table-placeholder",
                                    className="mb-4 p-3 border rounded")
                        ], width=12, className="mb-4")
                    ]),

                    # Active User Issue Creation Engagement Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("📝 Active User Issue Creation Engagement table will load here",
                                    id="users-issues-table-placeholder",
                                    className="mb-4 p-3 border rounded")
                        ], width=12, className="mb-4")
                    ]),
                ])

            ], fluid=True)
        ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh"})

    except Exception as e:
        logger.error(f"Error in users layout: {e}")
        return html.Div([
            dbc.Alert(f"Error: {str(e)}", color="danger"),
            html.P("Users layout failed to load.")
        ])
