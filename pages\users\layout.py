from dash import html
import dash_bootstrap_components as dbc
import logging
from components.cards import create_metric_card
from kpi_calculator import get_user_security_kpis

logger = logging.getLogger(__name__)

def layout():
    """
    COMPLETELY CLEAN USERS PAGE LAYOUT - NO UNWANTED SECTIONS!
    Only contains: User Demographics + Security & Permissions + User Analysis Tables
    """
    try:
        logger.info("👥 RENDERING COMPLETELY CLEAN USERS PAGE - NO UNWANTED SECTIONS!")

        # Get real KPI data from load schema
        try:
            kpi_data = get_user_security_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} User & Security KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading User KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_users': 0, 'active_users': 0, 'inactive_users': 0, 'external_users': 0,
                'users_in_groups': 0, 'total_groups': 0, 'users_with_roles': 0, 'permission_schemes': 0,
                'scheme_permissions': 0, 'users_with_issues': 0, 'users_with_worklogs': 0,
                'user_activity_score': 0, 'security_risk_score': 0, 'user_migration_complexity': 0,
                'orphaned_users': 0, 'admin_users': 0, 'user_account_health': 0
            }

        # 👥 USER & SECURITY ANALYSIS - 17 REAL KPIs
        # User Demographics (6)
        user_demographics_cards = [
            create_metric_card("Total Users", kpi_data['total_users'], "All users", "#0079BF"),
            create_metric_card("Active Users", kpi_data['active_users'], "Currently active", "#61BD4F"),
            create_metric_card("Inactive Users", kpi_data['inactive_users'], "Inactive accounts", "#EB5A46"),
            create_metric_card("External Users", kpi_data['external_users'], "Email usernames", "#F2D600"),
            create_metric_card("Users in Groups", kpi_data['users_in_groups'], "Group members", "#FFAB4A"),
            create_metric_card("Total Groups", kpi_data['total_groups'], "All groups", "#C377E0")
        ]

        # Security & Permissions (6)
        security_permissions_cards = [
            create_metric_card("Users with Roles", kpi_data['users_with_roles'], "Have roles", "#00C7E6"),
            create_metric_card("Permission Schemes", kpi_data['permission_schemes'], "Permission schemes", "#51E898"),
            create_metric_card("Scheme Permissions", kpi_data['scheme_permissions'], "Total permissions", "#FF78CB"),
            create_metric_card("Users with Issues", kpi_data['users_with_issues'], "Issue reporters", "#4ECDC4"),
            create_metric_card("Users with Worklogs", kpi_data['users_with_worklogs'], "Time loggers", "#0079BF"),
            create_metric_card("Admin Users", kpi_data['admin_users'], "Admin accounts", "#61BD4F")
        ]





        return html.Div([
            dbc.Container([
                # DEBUG MESSAGE - CONFIRM CLEAN LAYOUT IS LOADING
                dbc.Alert([
                    html.H4("🎯 CLEAN USERS PAGE LOADED!", className="alert-heading"),
                    html.P("This is the NEW clean layout with NO unwanted sections!", className="mb-0")
                ], color="success", className="mb-4"),

                # Page Title
                html.Div([
                    html.H2("User & Security Analysis", className="page-title mb-3"),
                    html.P("Comprehensive user analysis and security metrics - 12 KPIs",
                           className="page-subtitle mb-4")
                ]),

                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="users-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),

                # User Demographics Section (6 KPIs)
                html.Div([
                    html.H4("👤 User Demographics", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in user_demographics_cards[:3]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in user_demographics_cards[3:]], className="mb-4"),
                ]),

                # Security & Permissions Section (6 KPIs)
                html.Div([
                    html.H4("🔒 Security & Permissions", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in security_permissions_cards[:3]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in security_permissions_cards[3:]], className="mb-4"),
                ]),

                # User Data Tables Section
                html.Div([
                    html.H4("📋 User Analysis Tables", className="section-title mb-3"),

                    # Refresh button for tables
                    dbc.Row([
                        dbc.Col([
                            dbc.Button(
                                [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                                id="users-table-refresh",
                                color="primary",
                                className="mb-4"
                            )
                        ], width="auto"),
                    ]),

                    # User List Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("👥 User List table will load here", id="users-list-table-placeholder")
                        ], width=12, className="mb-4")
                    ]),

                    # User Worklog Contribution Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("⏰ User Worklog Contribution table will load here", id="users-worklog-table-placeholder")
                        ], width=12, className="mb-4")
                    ]),

                    # Active User Issue Creation Engagement Table
                    dbc.Row([
                        dbc.Col([
                            html.Div("📝 Active User Issue Creation Engagement table will load here", id="users-issues-table-placeholder")
                        ], width=12, className="mb-4")
                    ]),
                ]),




            ], fluid=True)
        ], style={
            "backgroundColor": "#F4F5F7",
            "minHeight": "100vh"
        })

    except Exception as e:
        logger.error(f"Error rendering users layout: {e}")
        return html.Div([
            dbc.Alert(f"Error loading user data: {str(e)}", color="danger"),
            html.P("Please check the data connection and try again.")
        ])