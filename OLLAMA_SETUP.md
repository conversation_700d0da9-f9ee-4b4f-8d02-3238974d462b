# 🤖 Ollama Setup for Jira Analytics Chatbot

## Quick Setup (5 minutes)

### 1. Install Ollama
- **Windows**: Download from https://ollama.ai/download/windows
- **Mac**: Download from https://ollama.ai/download/mac  
- **Linux**: `curl -fsSL https://ollama.ai/install.sh | sh`

### 2. Start Ollama
```bash
# Ollama runs automatically after installation
# Or manually start with:
ollama serve
```

### 3. Pull the AI Model
```bash
# Download the free Llama 3.2 model (2GB)
ollama pull llama3.2
```

### 4. Test Ollama
```bash
# Test if working
ollama list
# Should show llama3.2 model

# Test chat
ollama run llama3.2
# Type a question and press Enter
# Type /bye to exit
```

### 5. Start Jira Analytics
```bash
python app.py
```

## ✅ Features
- **100% Free** - No API keys needed
- **Runs Locally** - Your data stays private
- **Fast Responses** - No internet required after setup
- **<PERSON><PERSON>pert** - Trained for migration assistance

## 🔧 Troubleshooting
- **Port 11434**: Ollama uses this port by default
- **Model Size**: llama3.2 is ~2GB download
- **Fallback**: App works without Ollama (uses knowledge base)

## 🚀 Ready!
Your chatbot will automatically detect Ollama and provide AI-powered Jira migration assistance!
