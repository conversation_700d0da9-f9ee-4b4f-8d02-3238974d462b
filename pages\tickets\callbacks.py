from core.callback_manager import CallbackManager
from dash import Input, Output
import dash_bootstrap_components as dbc
from .layout import layout
import logging
import html

logger = logging.getLogger(__name__)

def register_callbacks(callback_manager):
    # DISABLED: This callback was trying to update "tickets-content" which doesn't exist
    # The main navigation callback handles the layout loading
    # This was causing the old layout with charts to be displayed
    import logging
    logger = logging.getLogger(__name__)
    logger.info("🚫 TICKETS CALLBACKS DISABLED - Main navigation handles layout")
    pass