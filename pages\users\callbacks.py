from dash import Input, Output
import logging
import pandas as pd
import dash_bootstrap_components as dbc

logger = logging.getLogger(__name__)

def register_callbacks(callback_manager):
    """
    Register all callbacks for the users page
    """
    try:
        # REMOVED CONFLICTING CALLBACK - WAS CAUSING ISSUES
        # The main navigation callback handles page-content
        # Individual page refresh should only refresh specific components

        # Add table refresh callback (similar to Projects page)
        @callback_manager.register(
            [Output("users-list-table-placeholder", "children"),
             Output("users-worklog-table-placeholder", "children"),
             Output("users-issues-table-placeholder", "children")],
            [Input("users-table-refresh", "n_clicks")]
        )
        def update_users_tables(n_clicks):
            logger.info(f"👥 USERS TABLE REFRESH TRIGGERED: n_clicks={n_clicks}")
            if n_clicks is None:
                logger.info("No clicks, returning empty outputs")
                return "", "", ""
            try:
                logger.info("👥 Loading User Tables Data")

                # Get user data tables
                try:
                    from kpi_calculator import JiraMigrationKPICalculator
                    import json

                    with open('dw_config.json', 'r') as f:
                        dw_config = json.load(f)
                    calculator = JiraMigrationKPICalculator(dw_config)

                    # Table 1: User List
                    users_list_query = '''
                        SELECT
                            user_name AS user_name,
                            CASE
                                WHEN active = 1 THEN 'Active'
                                WHEN active = 0 THEN 'Inactive'
                                ELSE 'Unknown'
                            END AS active_status
                        FROM load.dim_cwd_user
                        ORDER BY user_name
                    '''

                    users_list_data = calculator.execute_query(users_list_query)
                    users_list_df = pd.DataFrame(users_list_data, columns=["User Name", "Active Status"])

                    # Table 2: User Worklog Contribution
                    users_worklog_query = '''
                        SELECT
                            cu.user_name AS user_name,
                            dp.pname AS project_name,
                            ROUND(SUM(wl.timeworked) / 3600.0, 2) AS hours_worked
                        FROM load.dim_worklog wl
                        JOIN load.fact_jiraissue fji ON wl.issueid = fji.id
                        JOIN load.dim_cwd_user cu ON wl.author = cu.id
                        JOIN load.dim_project dp ON fji.project = dp.id
                        WHERE cu.active = 1
                        GROUP BY cu.user_name, dp.pname
                        ORDER BY hours_worked DESC
                    '''

                    users_worklog_data = calculator.execute_query(users_worklog_query)
                    users_worklog_df = pd.DataFrame(users_worklog_data, columns=["User Name", "Project Name", "Hours Worked"])

                    # Table 3: Active User Issue Creation Engagement
                    users_issues_query = '''
                        SELECT
                            cu.user_name AS user_name,
                            CASE
                                WHEN cu.active = 1 THEN 'Active'
                                WHEN cu.active = 0 THEN 'Inactive'
                                ELSE 'Unknown'
                            END AS active_status,
                            COUNT(fji.id) AS issues_created
                        FROM load.dim_cwd_user cu
                        LEFT JOIN load.fact_jiraissue fji ON fji.creator = cu.id
                        WHERE cu.active = 1
                        GROUP BY cu.user_name, cu.active
                        ORDER BY issues_created DESC, user_name
                    '''

                    users_issues_data = calculator.execute_query(users_issues_query)
                    users_issues_df = pd.DataFrame(users_issues_data, columns=["User Name", "Active Status", "Issues Created"])

                except Exception as e:
                    logger.error(f"Error loading user tables: {e}")
                    users_list_df = pd.DataFrame(columns=["User Name", "Active Status"])
                    users_worklog_df = pd.DataFrame(columns=["User Name", "Project Name", "Hours Worked"])
                    users_issues_df = pd.DataFrame(columns=["User Name", "Active Status", "Issues Created"])

                # Create data tables
                from components.tables import create_data_table
                users_list_table = create_data_table(
                    df=users_list_df,
                    columns=["User Name", "Active Status"],
                    title="👥 User List"
                )

                users_worklog_table = create_data_table(
                    df=users_worklog_df,
                    columns=["User Name", "Project Name", "Hours Worked"],
                    title="⏰ User Worklog Contribution"
                )

                users_issues_table = create_data_table(
                    df=users_issues_df,
                    columns=["User Name", "Active Status", "Issues Created"],
                    title="📝 Active User Issue Creation Engagement"
                )

                # Return the 3 tables to populate the placeholders
                return users_list_table, users_worklog_table, users_issues_table

            except Exception as e:
                logger.error(f"Error in update_users_tables: {str(e)}")
                error_msg = dbc.Alert(f"Error: {str(e)}", color="danger")
                return error_msg, error_msg, error_msg



    except Exception as e:
        logger.error(f"Error registering users callbacks: {str(e)}")
        raise 