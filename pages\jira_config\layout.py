# jira config layout
from dash import html
import dash_bootstrap_components as dbc

def layout():
    return html.Div([
        html.Div([
            html.I(className="fas fa-cogs me-2", style={"color": "#4e73df"}),
            html.Span("Configure Jira Instances", style={
                "font-size": "1.25rem",
                "font-weight": "600",
                "color": "#5a5c69",
                "font-family": "'Nunito', sans-serif"
            })
        ], className="d-flex align-items-center mb-2"),



        dbc.Card([
            dbc.CardHeader([
                html.I(className="fas fa-plus-circle me-2", style={"color": "#1cc88a"}),
                "Add Jira Instance"
            ], className="card-header-custom"),
            dbc.CardBody([
                dbc.Form([
                    dbc.Row([
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-server me-1"),
                                "Host"
                            ], html_for="jira-db-host", className="form-label"),
                            dbc.Input(id="jira-db-host", placeholder="localhost", value="localhost", className="form-control")
                        ], width=6),
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-plug me-1"),
                                "Port"
                            ], html_for="jira-db-port", className="form-label"),
                            dbc.Input(id="jira-db-port", type="number", placeholder="5432", value="5432", className="form-control")
                        ], width=6),
                    ], className="mb-2"),
                    dbc.Row([
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-user me-1"),
                                "Username"
                            ], html_for="jira-db-user", className="form-label"),
                            dbc.Input(id="jira-db-user", placeholder="jirauser", value="jirauser", className="form-control")
                        ], width=6),
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-lock me-1"),
                                "Password"
                            ], html_for="jira-db-password", className="form-label"),
                            dbc.Input(id="jira-db-password", type="password", placeholder="Enter password", className="form-control")
                        ], width=6),
                    ], className="mb-2"),
                    dbc.Row([
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-database me-1"),
                                "Database Name"
                            ], html_for="jira-db-name", className="form-label"),
                            dbc.Input(id="jira-db-name", placeholder="jiradb", value="jiradb", className="form-control")
                        ], width=6),
                        dbc.Col([
                            dbc.Label([
                                html.I(className="fas fa-building me-1"),
                                "Client Name"
                            ], html_for="client-name", className="form-label"),
                            dbc.Input(id="client-name", placeholder="ClientABC", className="form-control")
                        ], width=6),
                    ], className="mb-2"),
                    dbc.Row([
                        dbc.Col([
                            dbc.Button([
                                html.I(className="fas fa-plus me-2"),
                                "Add Instance"
                            ], id={"type": "jira-submit", "index": "jira"}, color="primary", className="btn-primary me-2"),
                            dbc.Button([
                                html.I(className="fas fa-eraser me-2"),
                                "Clear Form"
                            ], id="clear-form-btn", color="secondary", className="btn-secondary")
                        ], width=12)
                    ], className="mt-2"),
                    html.Div(id="jira-config-output", className="mt-2"),
                ])
            ], className="card-body-custom")
        ], className="card-custom mb-3"),

        # Added instances list
        dbc.Card([
            dbc.CardHeader([
                html.Div([
                    html.I(className="fas fa-list me-2", style={"color": "#4e73df"}),
                    html.H5("Added Jira Instances", className="mb-0 d-inline"),
                ], className="d-flex align-items-center"),
                html.Small(id="instance-count", className="text-muted")
            ], className="card-header-custom"),
            dbc.CardBody([
                html.Div(id="jira-instance-list", className="instance-list"),
                html.Div(id="duplicate-instance-message", className="mt-2"),
                dbc.Button([
                    html.I(className="fas fa-arrow-right me-2"),
                    "Continue to Dashboard"
                ], id="continue-btn", color="success", className="btn-success w-100 mt-3", disabled=True)
            ], className="card-body-custom")
        ], className="card-custom")
    ], className="modal-content-custom")