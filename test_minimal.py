#!/usr/bin/env python3
"""
MINIMAL TEST TO CHECK IF BASIC APP STARTS
"""

import dash
import dash_bootstrap_components as dbc
from dash import html
from flask import Flask

print("🔧 Starting minimal test...")

server = Flask(__name__)
app = dash.Dash(__name__, server=server, external_stylesheets=[dbc.themes.BOOTSTRAP])

app.layout = html.Div([
    html.H1("🚨 MINIMAL TEST", style={"color": "red", "textAlign": "center"}),
    html.H2("If you see this, basic Dash is working!", style={"color": "green", "textAlign": "center"})
])

print("✅ Layout created")

if __name__ == "__main__":
    print("🚀 Starting minimal app on port 8051...")
    app.run(debug=True, host="127.0.0.1", port=8051)
