from dash import html
import dash_bootstrap_components as dbc
import pandas as pd
import logging
from components.cards import create_metric_card
from components.tables import create_data_table
from kpi_calculator import get_project_business_kpis

logger = logging.getLogger(__name__)

def layout():
    try:
        logger.info("🏢 Rendering Project & Business Analysis layout with 8 KPIs + 2 data tables")

        # Get real KPI data from load schema
        try:
            kpi_data = get_project_business_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} Project & Business KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading Project KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_projects': 0, 'active_projects': 0, 'projects_with_issues': 0,
                'projects_with_components': 0, 'average_issues_per_project': 0, 'largest_project_issues': 0,
                'projects_with_custom_fields': 0, 'projects_with_attachments': 0, 'projects_with_worklogs': 0,
                'project_activity_score': 0, 'project_health_score': 0, 'projects_needing_cleanup': 0,
                'business_value_score': 0, 'project_migration_complexity': 0, 'project_readiness_score': 0
            }

        # 🏢 PROJECT & BUSINESS ANALYSIS - 8 CORE KPIs + 2 DATA TABLES
        # Core Project Metrics (8)
        core_project_cards = [
            create_metric_card("Total Projects", kpi_data['total_projects'], "All projects", "#0079BF"),
            create_metric_card("Active Projects", kpi_data['active_projects'], "Recent activity", "#61BD4F"),
            create_metric_card("Projects with Issues", kpi_data['projects_with_issues'], "Have issues", "#F2D600"),
            create_metric_card("Projects with Components", kpi_data['projects_with_components'], "Have components", "#FFAB4A"),
            create_metric_card("Avg Issues/Project", f"{kpi_data['average_issues_per_project']}", "Average issues", "#C377E0"),
            create_metric_card("Largest Project", kpi_data['largest_project_issues'], "Most issues", "#00C7E6"),
            create_metric_card("Projects w/ Custom Fields", kpi_data['projects_with_custom_fields'], "Custom fields", "#51E898"),
            create_metric_card("Projects w/ Attachments", kpi_data['projects_with_attachments'], "Have attachments", "#FF78CB")
        ]

        # Placeholder for data tables - will be loaded via callbacks
        projects_table = html.Div("📊 Project Issue Workload by Lead table will load here", id="projects-table-placeholder")
        attachments_table = html.Div("📎 File Attachment Inventory table will load here", id="attachments-table-placeholder")



        return html.Div([
            dbc.Container([
                # Page Title
                html.Div([
                    html.H2("Project & Business Analysis", className="page-title mb-4")
                ]),

                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="projects-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),

                # Core Project Metrics Section (8 KPIs)
                html.Div([
                    html.H4("📊 Core Project Metrics", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in core_project_cards[:4]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in core_project_cards[4:]], className="mb-4"),
                ]),

                # Project Data Tables Section
                html.Div([
                    html.H4("📋 Project Analysis Tables", className="section-title mb-3"),

                    # Project Issue Workload by Lead Table
                    dbc.Row([
                        dbc.Col([
                            projects_table
                        ], width=12, className="mb-4")
                    ]),

                    # File Attachment Inventory Table
                    dbc.Row([
                        dbc.Col([
                            attachments_table
                        ], width=12, className="mb-4")
                    ]),
                ]),


            ], fluid=True)
        ], style={
            "backgroundColor": "#F4F5F7",
            "minHeight": "100vh"
        })

    except Exception as e:
        logger.error(f"Error rendering projects layout: {e}")
        return html.Div([
            dbc.Alert(f"Error loading project data: {str(e)}", color="danger"),
            html.P("Please check the data connection and try again.")
        ])