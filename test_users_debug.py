#!/usr/bin/env python3
"""
DEBUG SCRIPT TO TEST USERS PAGE LAYOUT
This will help us identify if there's a module caching or import issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_users_layout():
    """Test the users layout directly"""
    print("🔍 TESTING USERS LAYOUT DIRECTLY...")
    
    try:
        # Import the users layout
        from pages.users.layout import layout as users_layout
        
        # Call the layout function
        layout_result = users_layout()
        
        # Convert to string to analyze content
        layout_str = str(layout_result)
        
        print(f"📄 Layout type: {type(layout_result)}")
        print(f"📏 Layout string length: {len(layout_str)}")
        
        # Check for unwanted content
        unwanted_phrases = [
            "Analysis & Risk Assessment",
            "User Analysis Charts",
            "Risk Assessment",
            "Migration Risk Assessment"
        ]
        
        found_unwanted = []
        for phrase in unwanted_phrases:
            if phrase in layout_str:
                found_unwanted.append(phrase)
        
        if found_unwanted:
            print("❌ FOUND UNWANTED CONTENT:")
            for phrase in found_unwanted:
                print(f"   - {phrase}")
        else:
            print("✅ NO UNWANTED CONTENT FOUND")
        
        # Check for expected content
        expected_phrases = [
            "Users & Security Analysis",
            "User List table will load here",
            "User Worklog Contribution",
            "Active User Issue Creation"
        ]
        
        found_expected = []
        for phrase in expected_phrases:
            if phrase in layout_str:
                found_expected.append(phrase)
        
        print(f"✅ FOUND EXPECTED CONTENT: {len(found_expected)}/{len(expected_phrases)}")
        for phrase in found_expected:
            print(f"   - {phrase}")
        
        # Show first 500 characters
        print("\n📝 FIRST 500 CHARACTERS OF LAYOUT:")
        print(layout_str[:500])
        
        return layout_result
        
    except Exception as e:
        print(f"❌ ERROR TESTING USERS LAYOUT: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_users_layout()
