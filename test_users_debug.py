#!/usr/bin/env python3
"""
SIMPLE DIRECT TEST - NO IMPORTS
"""

print("🔍 TESTING USERS LAYOUT DIRECTLY...")
print("=" * 50)

try:
    # Test 1: Check if we can import
    print("📦 Testing import...")
    from pages.users.layout import layout as users_layout
    print("✅ Import successful")

    # Test 2: Call the function
    print("🔧 Calling layout function...")
    layout_result = users_layout()
    print("✅ Layout function called successfully")

    # Test 3: Convert to string and check content
    print("📝 Converting to string...")
    layout_str = str(layout_result)
    print(f"✅ String conversion successful - Length: {len(layout_str)}")

    # Test 4: Search for unwanted content
    print("🔍 Searching for unwanted content...")
    if "Analysis & Risk Assessment" in layout_str:
        print("❌ FOUND: 'Analysis & Risk Assessment'")
    else:
        print("✅ NOT FOUND: 'Analysis & Risk Assessment'")

    if "User Analysis Charts" in layout_str:
        print("❌ FOUND: 'User Analysis Charts'")
    else:
        print("✅ NOT FOUND: 'User Analysis Charts'")

    # Test 5: Search for expected content
    print("🔍 Searching for expected content...")
    if "Users & Security Analysis" in layout_str:
        print("✅ FOUND: 'Users & Security Analysis'")
    else:
        print("❌ NOT FOUND: 'Users & Security Analysis'")

    # Test 6: Show sample content
    print("\n📄 FIRST 200 CHARACTERS:")
    print(repr(layout_str[:200]))

except Exception as e:
    print(f"❌ ERROR: {e}")
    import traceback
    traceback.print_exc()

print("=" * 50)
print("🏁 TEST COMPLETE")
