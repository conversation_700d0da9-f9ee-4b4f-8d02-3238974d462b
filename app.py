import dash
import dash_bootstrap_components as dbc
from dash import Input, Output, State, html, dcc, no_update
from flask import Flask
import logging
import atexit
from components.sidebar import get_sidebar
from components.header import get_header
from core.callback_manager import CallbackManager
from core.state_manager import AppState
from pages.dw_config.layout import layout as dw_config_layout
from pages.jira_config.layout import layout as jira_config_layout
from pages.home.layout import layout as home_layout
from pages.projects.layout import layout
from pages.tickets.layout import layout as tickets_layout
from pages.workflows.layout import layout as workflows_layout
# from pages.diagrams.layout import layout as diagrams_layout  # Module doesn't exist
from pages.users.layout import layout as users_layout
from pages.configuration.layout import layout as configuration_layout
from pages.agile.layout import layout as agile_layout
# from chatbot.frontend.layout import layout as chatbot_layout  # TEMPORARILY DISABLED
from gestuser.layout import login_layout, register_layout

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

server = Flask(__name__)
app = dash.Dash(
    __name__,
    server=server,
    external_stylesheets=[
        dbc.themes.BOOTSTRAP,
        dbc.icons.BOOTSTRAP,
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
    ],
    suppress_callback_exceptions=True
)

app_state = AppState()
callback_manager = CallbackManager(app)

app.layout = html.Div([
    dcc.Location(id="url"),
    dcc.Store(id="form-state-store", data=app_state.state),
    dcc.Store(id="session-store", storage_type="session"),  # Session management

    # Dynamic layout container - will contain either auth pages or main app
    # COMPLETELY EMPTY - navigation callback will populate this
    html.Div(id="app-container", children=[]),

    dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle("Data Warehouse Configuration"), className="modal-header-custom"),
            dbc.ModalBody(dw_config_layout()),
            dbc.ModalFooter(),
        ],
        id="dw-config-modal",
        is_open=False,  # Opens after authentication
        centered=True,
        backdrop="static",
        className="modal-custom modal-compact"
    ),

    dbc.Modal(
        [
            dbc.ModalHeader(dbc.ModalTitle("Jira Configuration"), className="modal-header-custom"),
            dbc.ModalBody(jira_config_layout()),
            dbc.ModalFooter(),
        ],
        id="jira-config-modal",
        is_open=False,
        centered=True,
        backdrop="static",
        className="modal-custom modal-compact"
    ),

    html.Link(rel="stylesheet", href="/assets/custom.css"),
])

# ===== REGISTER ALL CALLBACKS IMMEDIATELY AFTER LAYOUT =====
print("🔧 REGISTERING CALLBACKS IMMEDIATELY AFTER LAYOUT...")

# Import all callback functions
# from components.header_callbacks import register_header_callbacks  # DISABLED - CAUSING CONFLICTS
from gestuser.callbacks_simple import register_auth_callbacks

# Register callbacks in order
print("🔧 Header callbacks completely disabled - file removed to prevent conflicts")

try:
    register_auth_callbacks(callback_manager)
    print("✅ Auth callbacks registered")
except Exception as e:
    print(f"❌ Auth callbacks failed: {e}")

# Register DW config callbacks
try:
    from pages.dw_config.callbacks import register_callbacks as register_dw_callbacks
    register_dw_callbacks(callback_manager, app_state)
    print("✅ DW config callbacks registered")
except Exception as e:
    print(f"❌ DW config callbacks failed: {e}")

# Register Jira config callbacks
try:
    from pages.jira_config.callbacks import register_callbacks as register_jira_callbacks
    register_jira_callbacks(callback_manager, app_state)
    print("✅ Jira config callbacks registered")
except Exception as e:
    print(f"❌ Jira config callbacks failed: {e}")

# Register Home page callbacks (including ETL button)
try:
    from pages.home.callbacks import register_callbacks as register_home_callbacks
    register_home_callbacks(callback_manager)
    print("✅ Home callbacks registered (ETL button enabled)")
except Exception as e:
    print(f"❌ Home callbacks failed: {e}")

# Register other page callbacks
try:
    from pages.projects.callbacks import register_callbacks as register_projects_callbacks
    register_projects_callbacks(callback_manager)
    print("✅ Projects callbacks registered")
except Exception as e:
    print(f"❌ Projects callbacks failed: {e}")

try:
    from pages.tickets.callbacks import register_callbacks as register_tickets_callbacks
    register_tickets_callbacks(callback_manager)
    print("✅ Tickets callbacks registered")
except Exception as e:
    print(f"❌ Tickets callbacks failed: {e}")

try:
    from pages.users.callbacks import register_callbacks as register_users_callbacks
    register_users_callbacks(callback_manager)
    print("✅ Users callbacks registered")
except Exception as e:
    print(f"❌ Users callbacks failed: {e}")

try:
    from pages.workflows.callbacks import register_callbacks as register_workflows_callbacks
    register_workflows_callbacks(callback_manager)
    print("✅ Workflows callbacks registered")
except Exception as e:
    print(f"❌ Workflows callbacks failed: {e}")

# Register Sidebar callbacks
try:
    from components.sidebar import register_sidebar_callbacks
    register_sidebar_callbacks(callback_manager)
    print("✅ Sidebar callbacks registered")
except Exception as e:
    print(f"❌ Sidebar callbacks failed: {e}")

# Register Chatbot callbacks - TEMPORARILY DISABLED
# try:
#     from chatbot.frontend.callbacks import register_callbacks as register_chatbot_callbacks
#     register_chatbot_callbacks(callback_manager)
#     print("✅ Chatbot callbacks registered")
# except Exception as e:
#     print(f"❌ Chatbot callbacks failed: {e}")
print("🤖 Chatbot callbacks temporarily disabled for testing")

# Navigation callback - returns complete layout structure
@app.callback(
    Output("app-container", "children"),
    [Input("url", "pathname"),
     Input("session-store", "data")],
    prevent_initial_call=False
)
def handle_navigation(pathname, session_data):
    print(f"🔄 NAVIGATION CALLBACK TRIGGERED: {pathname}")
    logger.info(f"🔄 NAVIGATION: {pathname}")

    try:
        # Handle None or empty pathname - default to login
        if pathname is None or pathname == "":
            pathname = "/login"
            print("🔄 No pathname provided, defaulting to /login")

        # Authentication pages - full screen, no sidebar/header
        if pathname == "/login":
            print("🔐 Returning LOGIN layout (no sidebar/header)")
            return login_layout()

        elif pathname == "/register":
            print("🔐 Returning REGISTER layout (no sidebar/header)")
            return register_layout()

        # Main app pages - check authentication first
        else:
            # Check if user is authenticated
            if not session_data or not session_data.get("logged_in", False):
                print("🔐 User not authenticated, showing login")
                return login_layout()

            print(f"🏠 Returning MAIN APP layout for: {pathname}")
            # Determine page content
            if pathname == "/" or pathname == "/home":
                page_content = home_layout()  # 📊 Migration Overview (16 KPIs)
            elif pathname == "/projects":
                page_content = layout()  # 🏢 Project & Business Analysis (15 KPIs)
            elif pathname == "/users":
                page_content = users_layout()  # 👥 User & Security Audit (17 KPIs)
            elif pathname == "/configuration":
                page_content = configuration_layout()  # 🔧 Configuration & Fields (18 KPIs)
            elif pathname == "/tickets":
                page_content = tickets_layout()  # 📋 Issues & Content (16 KPIs)
            elif pathname == "/workflows":
                page_content = workflows_layout()  # 🔄 Workflow & Automation (14 KPIs)
            elif pathname == "/agile":
                page_content = agile_layout()  # 📈 Agile & Boards (14 KPIs)
            elif pathname == "/chatbot":
                page_content = html.Div("🤖 Chatbot temporarily disabled for testing")  # 🤖 AI Assistant
            elif pathname == "/diagrams":
                page_content = html.Div("Diagrams page not implemented yet")
            else:
                page_content = html.Div("404 - Page not found")

            # Return complete main app layout with sidebar, header, and content
            return html.Div([
                get_sidebar(),
                get_header(),
                html.Div(page_content, id="page-content", className="page-content")
            ], id="main-app-container", className="main-app-wrapper")

    except Exception as e:
        logger.error(f"❌ NAVIGATION ERROR: {pathname}: {str(e)}")
        print(f"❌ NAVIGATION ERROR: {pathname}: {str(e)}")
        # Return error in main app layout
        return html.Div([
            get_sidebar(),
            get_header(),
            html.Div(
                html.Div(f"Error loading page: {str(e)}"),
                id="page-content",
                className="page-content"
            )
        ], id="main-app-container", className="main-app-wrapper")

print("🔄 NAVIGATION CALLBACK REGISTERED")

# Auto-open DW config modal ONLY ONCE after successful login
@app.callback(
    Output("dw-config-modal", "is_open"),
    [Input("session-store", "data")],
    prevent_initial_call=False
)
def auto_open_dw_config_modal(session_data):
    """
    Open DW config modal only once after successful login.
    Uses session storage to track if modals have been shown.
    """
    if not session_data:
        return False

    # Check if user is logged in and modals haven't been shown yet
    is_logged_in = session_data.get("logged_in", False)
    modals_shown = session_data.get("modals_shown", False)
    etl_completed = session_data.get("etl_completed", False)

    # ONLY open modal if:
    # 1. User is logged in
    # 2. Modals haven't been shown yet
    # 3. ETL hasn't been completed (to avoid reopening after ETL)
    if is_logged_in and not modals_shown and not etl_completed:
        print("🔧 Auto-opening DW Config modal (first time after login)")
        return True
    return False

# Auto-open Jira config modal after DW config is closed (only once)
@app.callback(
    [Output("jira-config-modal", "is_open"),
     Output("session-store", "data", allow_duplicate=True)],
    [Input("dw-config-modal", "is_open")],
    [State("session-store", "data")],
    prevent_initial_call=True
)
def auto_open_jira_config_modal(dw_modal_is_open, session_data):
    """
    When DW config modal is closed, open Jira config modal and mark modals as shown.
    """
    session_data = session_data or {}

    # Only open Jira config modal if:
    # 1. DW config modal was just closed
    # 2. User is logged in
    # 3. Modals haven't been marked as shown yet (to prevent reopening)
    modals_shown = session_data.get("modals_shown", False)

    if not dw_modal_is_open and session_data.get("logged_in", False) and not modals_shown:
        print("🔧 Auto-opening Jira Config modal after DW config closed")
        # Mark modals as shown so they don't appear again
        session_data["modals_shown"] = True
        return True, session_data

    return False, session_data

print("🔧 Auto-modal callbacks registered")

@atexit.register
def cleanup():
    logger.info("Cleaning up resources")

if __name__ == "__main__":
    # Setup authentication database - temporarily disabled
    # try:
    #     from gestuser.database_setup import setup_auth_database
    #     setup_auth_database()
    # except Exception as e:
    #     print(f"⚠️ Authentication setup failed: {str(e)}")
    print("🔧 Database setup temporarily disabled for callback testing")

    # Show DW detection on startup - TEMPORARILY DISABLED FOR DEBUGGING
    print("=" * 60)
    print("🚀 JIRA ANALYTICS APP STARTING")
    print("🔐 AUTHENTICATION: Ready")
    print("🎯 DW DETECTION: Temporarily disabled for debugging")
    print("=" * 60)

    app.run(debug=False, host="127.0.0.1", port=8051)