from dash import html, dcc
import dash_bootstrap_components as dbc
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import logging
from components.cards import create_metric_card
from components.tables import create_data_table
from components.charts import create_bar_chart, create_pie_chart
from kpi_calculator import get_configuration_fields_kpis

logger = logging.getLogger(__name__)

def layout():
    try:
        logger.info("🔧 Rendering Configuration & Fields layout with 18 real KPIs")

        # Get real KPI data from load schema
        try:
            kpi_data = get_configuration_fields_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} Configuration & Fields KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading Configuration KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_custom_fields': 0, 'active_custom_fields': 0, 'unused_custom_fields': 0,
                'custom_field_values': 0, 'fields_per_project': 0, 'field_configurations': 0,
                'field_config_schemes': 0, 'field_screens': 0, 'field_screen_tabs': 0,
                'total_plugins': 0, 'managed_config_items': 0, 'configuration_complexity': 0,
                'field_usage_distribution': 'High: 0%, Medium: 0%, Low: 0%', 'screen_configuration_count': 0,
                'permission_complexity': 0, 'plugin_risk_assessment': 0, 'migration_blockers': 0,
                'config_cleanup_required': 0
            }

        # 🔧 CONFIGURATION & FIELDS - 18 REAL KPIs
        # Custom Fields Analysis (6)
        custom_fields_cards = [
            create_metric_card("Total Custom Fields", kpi_data['total_custom_fields'], "All custom fields", "#0079BF"),
            create_metric_card("Active Custom Fields", kpi_data['active_custom_fields'], "With data", "#61BD4F"),
            create_metric_card("Unused Custom Fields", kpi_data['unused_custom_fields'], "No data", "#EB5A46"),
            create_metric_card("Custom Field Values", kpi_data['custom_field_values'], "Total values", "#F2D600"),
            create_metric_card("Fields per Project", f"{kpi_data['fields_per_project']}", "Average fields", "#FFAB4A"),
            create_metric_card("Field Configurations", kpi_data['field_configurations'], "Field configs", "#C377E0")
        ]
        
        # Configuration Management (6)
        config_management_cards = [
            create_metric_card("Config Schemes", kpi_data['field_config_schemes'], "Field schemes", "#00C7E6"),
            create_metric_card("Field Screens", kpi_data['field_screens'], "Screen configs", "#51E898"),
            create_metric_card("Screen Tabs", kpi_data['field_screen_tabs'], "Screen tabs", "#FF78CB"),
            create_metric_card("Total Plugins", kpi_data['total_plugins'], "Installed plugins", "#4ECDC4"),
            create_metric_card("Managed Config Items", kpi_data['managed_config_items'], "Config items", "#0079BF"),
            create_metric_card("Config Complexity", f"{kpi_data['configuration_complexity']}/10", "Complexity score", "#61BD4F")
        ]
        
        # Analysis & Risk Assessment (6)
        risk_assessment_cards = [
            create_metric_card("Field Usage", kpi_data['field_usage_distribution'], "Usage pattern", "#F2D600"),
            create_metric_card("Screen Configs", kpi_data['screen_configuration_count'], "Total screens", "#FFAB4A"),
            create_metric_card("Permission Complexity", kpi_data['permission_complexity'], "Permission items", "#C377E0"),
            create_metric_card("Plugin Risk", f"{kpi_data['plugin_risk_assessment']}/10", "Plugin risk", "#EB5A46"),
            create_metric_card("Migration Blockers", kpi_data['migration_blockers'], "Critical issues", "#00C7E6"),
            create_metric_card("Cleanup Required", kpi_data['config_cleanup_required'], "Items to clean", "#51E898")
        ]
        
        # Combine all 18 KPI cards
        all_kpi_cards = custom_fields_cards + config_management_cards + risk_assessment_cards
        
        # REMOVED: All chart data and chart creation - keeping only cards

        # --- 🔧 CONFIGURATION & FIELDS LAYOUT (18 KPIs) ---
        return html.Div([
            dbc.Container([
                # Buttons
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="refresh-btn",
                            color="primary",
                            className="me-2"
                        ),
                        dbc.Button(
                            [html.I(className="fas fa-file-download me-2"), html.Span("Generate Report")],
                            id="generate-report-btn",
                            color="success"
                        )
                    ], width="auto"),
                ], className="mb-4", justify="start"),
                
                # Page Title
                html.Div([
                    html.H2("🔧 Configuration & Fields", className="page-title mb-4")
                ]),
                
                # Custom Fields Analysis Section (6 KPIs)
                html.Div([
                    html.H4("🎛️ Custom Fields Analysis", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in custom_fields_cards], className="mb-4"),
                ]),
                
                # Configuration Management Section (6 KPIs)
                html.Div([
                    html.H4("⚙️ Configuration Management", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in config_management_cards], className="mb-4"),
                ]),
                
                # Risk Assessment Section REMOVED per user request
                
                # REMOVED: Charts Section - keeping only cards as requested
            ], fluid=True)
        ], style={
            "backgroundColor": "#F4F5F7",
            "minHeight": "100vh"
        })

    except Exception as e:
        logger.error(f"Error rendering configuration layout: {e}")
        return html.Div([
            dbc.Alert(f"Error loading configuration data: {str(e)}", color="danger"),
            html.P("Please check the data connection and try again.")
        ])
