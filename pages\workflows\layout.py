from dash import html, dcc
import dash_bootstrap_components as dbc
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import logging
import psycopg2
import json
from components.cards import create_metric_card
from components.tables import create_data_table
from components.charts import create_bar_chart

logger = logging.getLogger(__name__)

def get_real_workflow_data():
    """Get real workflow data from database with actual relationships"""
    try:
        with open('dw_config.json', 'r') as f:
            dw_config = json.load(f)

        conn = psycopg2.connect(**dw_config)
        cursor = conn.cursor()

        # 1. Workflow Schemes and their usage
        cursor.execute("""
            SELECT
                ws.id,
                ws.name,
                ws.description,
                COUNT(DISTINCT wse.id) as entities,
                COUNT(DISTINCT ji.id) as issues_using
            FROM load.dim_workflowscheme ws
            LEFT JOIN load.dim_workflowschemeentity wse ON ws.id = wse.scheme
            LEFT JOIN load.fact_jiraissue ji ON ji.workflowscheme_id = ws.id
            GROUP BY ws.id, ws.name, ws.description
            ORDER BY issues_using DESC
        """)
        workflow_schemes = cursor.fetchall()

        # 2. Workflows and their complexity
        cursor.execute("""
            SELECT
                w.id,
                w.workflowname,
                LENGTH(w.descriptor) as complexity,
                COUNT(DISTINCT ji.id) as issues_count
            FROM load.dim_jiraworkflows w
            LEFT JOIN load.fact_jiraissue ji ON ji.jiraworkflows_id = w.id
            GROUP BY w.id, w.workflowname, w.descriptor
            ORDER BY issues_count DESC
        """)
        workflows = cursor.fetchall()

        # 3. Permission Schemes with actual permissions
        cursor.execute("""
            SELECT
                ps.id,
                ps.name,
                COUNT(DISTINCT sp.id) as permission_count,
                COUNT(DISTINCT ji.id) as issues_using
            FROM load.dim_permissionscheme ps
            LEFT JOIN load.dim_schemepermissions sp ON ps.id = sp.scheme
            LEFT JOIN load.fact_jiraissue ji ON ji.permissionscheme_id = ps.id
            GROUP BY ps.id, ps.name
            ORDER BY permission_count DESC
        """)
        permission_schemes = cursor.fetchall()

        # 4. JSM Audit Activity (if available)
        try:
            cursor.execute('SELECT COUNT(*) FROM load."dim_AO_C77861_AUDIT_ENTITY"')
            audit_count = cursor.fetchone()[0]
        except:
            audit_count = 0

        conn.close()

        return {
            'workflow_schemes': workflow_schemes,
            'workflows': workflows,
            'permission_schemes': permission_schemes,
            'audit_count': audit_count
        }

    except Exception as e:
        logger.error(f"Error getting real workflow data: {e}")
        return None

def layout():
    try:
        logger.info("🔄 Rendering Workflow & Automation Audit with REAL DATA TABLES")

        # Get real data from database
        real_data = get_real_workflow_data()
        if not real_data:
            return html.Div("❌ Error loading real workflow data")

        logger.info("✅ Loaded real workflow data with relationships")

        # Create real data tables instead of cards
        # 1. Workflow Schemes Table
        workflow_schemes_df = pd.DataFrame(real_data['workflow_schemes'],
                                         columns=['ID', 'Scheme Name', 'Description', 'Entities', 'Issues Using'])

        # 2. Workflows Table
        workflows_df = pd.DataFrame(real_data['workflows'],
                                  columns=['ID', 'Workflow Name', 'Complexity', 'Issues Count'])

        # 3. Permission Schemes Table
        permission_schemes_df = pd.DataFrame(real_data['permission_schemes'],
                                           columns=['ID', 'Permission Scheme', 'Permissions', 'Issues Using'])

        # Summary metrics from real data
        total_workflows = len(real_data['workflows'])
        total_schemes = len(real_data['workflow_schemes'])
        total_permissions = len(real_data['permission_schemes'])
        audit_records = real_data['audit_count']

        # Create real data charts from actual database data
        workflow_summary_data = pd.DataFrame({
            'Metric': ['Total Workflows', 'Workflow Schemes', 'Permission Schemes', 'Audit Records'],
            'Count': [total_workflows, total_schemes, total_permissions, audit_records]
        })

        # Top workflows by usage
        top_workflows = workflows_df.nlargest(5, 'Issues Count') if not workflows_df.empty else pd.DataFrame()

        # Top permission schemes by usage
        top_permissions = permission_schemes_df.nlargest(5, 'Issues Using') if not permission_schemes_df.empty else pd.DataFrame()

        # Create real data charts
        summary_chart = create_bar_chart(
            workflow_summary_data,
            value_cols=['Count'],
            label_col='Metric',
            title="Workflow & Permission Summary",
            color_scheme=["#0079BF"],
            id="workflow-summary-chart"
        )

        # Create real data tables
        workflow_schemes_table = create_data_table(
            workflow_schemes_df,
            columns=['ID', 'Scheme Name', 'Description', 'Entities', 'Issues Using'],
            title="🔄 Workflow Schemes Analysis"
        )

        workflows_table = create_data_table(
            workflows_df,
            columns=['ID', 'Workflow Name', 'Complexity', 'Issues Count'],
            title="⚙️ Workflows by Usage"
        )

        permission_schemes_table = create_data_table(
            permission_schemes_df,
            columns=['ID', 'Scheme Name', 'Description', 'Issues Using'],
            title="🔒 Permission Schemes Analysis"
        )

        return html.Div([
            dbc.Container([


                # Summary Metrics
                dbc.Row([
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(f"{total_workflows}", className="text-primary mb-0"),
                                html.P("Total Workflows", className="mb-0")
                            ])
                        ])
                    ], md=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(f"{total_schemes}", className="text-success mb-0"),
                                html.P("Workflow Schemes", className="mb-0")
                            ])
                        ])
                    ], md=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(f"{total_permissions}", className="text-warning mb-0"),
                                html.P("Permission Schemes", className="mb-0")
                            ])
                        ])
                    ], md=3),
                    dbc.Col([
                        dbc.Card([
                            dbc.CardBody([
                                html.H4(f"{audit_records:,}", className="text-info mb-0"),
                                html.P("Audit Records", className="mb-0")
                            ])
                        ])
                    ], md=3)
                ], className="mb-4"),

                # Real Data Tables Section
                html.Div([
                    html.H4("📊 Real Data Analysis", className="section-title mb-3"),

                    # Summary Chart
                    dbc.Row([
                        dbc.Col([
                            dbc.Card([
                                dbc.CardHeader([
                                    html.H5("Configuration Summary", className="mb-0 text-white")
                                ], style={"backgroundColor": "#0079BF"}),
                                dbc.CardBody(summary_chart)
                            ], className="mb-4")
                        ], md=12)
                    ]),

                    # Data Tables Row
                    dbc.Row([
                        # Workflow Schemes Table
                        dbc.Col([
                            dbc.Card([
                                dbc.CardHeader([
                                    html.H5("🔄 Workflow Schemes", className="mb-0 text-white")
                                ], style={"backgroundColor": "#61BD4F"}),
                                dbc.CardBody(workflow_schemes_table)
                            ], className="mb-4")
                        ], md=6),

                        # Permission Schemes Table
                        dbc.Col([
                            dbc.Card([
                                dbc.CardHeader([
                                    html.H5("🔒 Permission Schemes", className="mb-0 text-white")
                                ], style={"backgroundColor": "#F2D600"}),
                                dbc.CardBody(permission_schemes_table)
                            ], className="mb-4")
                        ], md=6)
                    ]),

                    # Workflows Table (Full Width)
                    dbc.Row([
                        dbc.Col([
                            dbc.Card([
                                dbc.CardHeader([
                                    html.H5("⚙️ Workflows by Usage", className="mb-0 text-white")
                                ], style={"backgroundColor": "#C377E0"}),
                                dbc.CardBody(workflows_table)
                            ], className="mb-4")
                        ], md=12)
                    ])
                ])
            ], fluid=True)
        ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh"})
    except Exception as e:
        logger.error(f"Error rendering workflows layout: {str(e)}")
        return [
            dbc.Alert(
                [
                    html.H4("Error", className="alert-heading"),
                    html.P(f"An error occurred while rendering the workflows page: {str(e)}")
                ],
                color="danger",
                dismissable=True
            )
        ]