#!/usr/bin/env python3
"""
🔄 CHATBOT CALLBACKS
Interactive functionality for the Jira Migration Chatbot
"""

from dash import html, Input, Output, State, callback_context, ctx
import dash_bootstrap_components as dbc
import logging
from datetime import datetime
from ..backend.ai_model import get_chatbot

logger = logging.getLogger(__name__)

def register_callbacks(callback_manager):
    """Register all chatbot callbacks - SIMPLIFIED VERSION"""

    @callback_manager.register(
        [Output("chat-messages", "children"),
         Output("chat-input", "value")],
        [Input("chat-send-btn", "n_clicks")],
        [State("chat-input", "value"),
         State("chat-messages", "children")]
    )
    def handle_chat_send(n_clicks, input_value, current_messages):
        """Handle chat send button"""
        try:
            if not n_clicks or not input_value:
                return current_messages or [], ""

            user_message = input_value.strip()
            if not user_message:
                return current_messages or [], ""

            # Get chatbot response
            chatbot = get_chatbot()
            bot_response = chatbot.get_response(user_message)

            # Create message components
            current_messages = current_messages or []

            # Add user message
            user_msg_component = create_user_message(user_message)
            current_messages.append(user_msg_component)

            # Add bot response
            bot_msg_component = create_bot_message(bot_response)
            current_messages.append(bot_msg_component)

            # Clear input and return updated messages
            return current_messages, ""

        except Exception as e:
            logger.error(f"Error in chat interaction: {e}")
            error_msg = create_bot_message("I apologize, but I encountered an error. Please try again.")
            return (current_messages or []) + [error_msg], ""

def create_user_message(message: str) -> html.Div:
    """Create a user message component"""
    timestamp = datetime.now().strftime("%H:%M")
    
    return html.Div([
        html.Div([
            html.Div([
                html.Div([
                    html.I(className="fas fa-user me-2"),
                    html.Strong("You"),
                    html.Small(f" • {timestamp}", className="text-muted ms-2")
                ], className="d-flex align-items-center mb-1"),
                html.P(message, className="mb-0")
            ], className="p-3", style={
                "backgroundColor": "#e3f2fd",
                "borderRadius": "1rem 1rem 0.25rem 1rem",
                "border": "1px solid #bbdefb"
            })
        ], className="d-flex justify-content-end mb-3"),
    ], className="user-message")

def create_bot_message(message: str) -> html.Div:
    """Create a bot message component"""
    timestamp = datetime.now().strftime("%H:%M")
    
    # Format message with markdown-like styling
    formatted_message = format_bot_message(message)
    
    return html.Div([
        html.Div([
            html.Div([
                html.Div([
                    html.I(className="fas fa-robot me-2", style={"color": "#0079BF"}),
                    html.Strong("Migration Assistant", style={"color": "#0079BF"}),
                    html.Small(f" • {timestamp}", className="text-muted ms-2")
                ], className="d-flex align-items-center mb-2"),
                html.Div(formatted_message, className="mb-0")
            ], className="p-3", style={
                "backgroundColor": "#f8f9fa",
                "borderRadius": "1rem 1rem 1rem 0.25rem",
                "border": "1px solid #dee2e6"
            })
        ], className="d-flex justify-content-start mb-3"),
    ], className="bot-message")

def format_bot_message(message: str) -> list:
    """Format bot message with proper styling"""
    components = []
    lines = message.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Headers with emojis
        if line.startswith('**') and line.endswith('**'):
            header_text = line.replace('**', '')
            components.append(html.H6(header_text, className="mb-2 text-primary"))
        
        # Bullet points
        elif line.startswith('•'):
            bullet_text = line[1:].strip()
            if '**' in bullet_text:
                # Bold text in bullet
                parts = bullet_text.split('**')
                bullet_content = []
                for i, part in enumerate(parts):
                    if i % 2 == 1:  # Odd indices are bold
                        bullet_content.append(html.Strong(part))
                    else:
                        bullet_content.append(part)
                components.append(html.Li(bullet_content, className="mb-1"))
            else:
                components.append(html.Li(bullet_text, className="mb-1"))
        
        # Numbered lists
        elif any(line.startswith(f"{i}.") for i in range(1, 10)):
            list_text = line[2:].strip()
            if '**' in list_text:
                # Bold text in list
                parts = list_text.split('**')
                list_content = []
                for i, part in enumerate(parts):
                    if i % 2 == 1:  # Odd indices are bold
                        list_content.append(html.Strong(part))
                    else:
                        list_content.append(part)
                components.append(html.Li(list_content, className="mb-1"))
            else:
                components.append(html.Li(list_text, className="mb-1"))
        
        # Regular paragraphs
        else:
            if '**' in line:
                # Bold text in paragraph
                parts = line.split('**')
                para_content = []
                for i, part in enumerate(parts):
                    if i % 2 == 1:  # Odd indices are bold
                        para_content.append(html.Strong(part))
                    else:
                        para_content.append(part)
                components.append(html.P(para_content, className="mb-2"))
            else:
                components.append(html.P(line, className="mb-2"))
    
    return components if components else [html.P(message, className="mb-0")]
