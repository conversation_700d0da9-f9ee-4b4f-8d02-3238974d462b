from dash import html, dcc
import dash_bootstrap_components as dbc
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import logging
from components.cards import create_metric_card
from components.tables import create_data_table
from components.charts import create_bar_chart, create_pie_chart
from kpi_calculator import get_agile_boards_kpis

logger = logging.getLogger(__name__)

def layout():
    try:
        logger.info("📈 Rendering Agile & Boards layout with 14 real KPIs")

        # Get real KPI data from load schema
        try:
            kpi_data = get_agile_boards_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} Agile & Boards KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading Agile KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_rapid_views': 0, 'active_boards': 0, 'board_usage_by_projects': 0,
                'agile_projects_count': 0, 'board_configuration_analysis': 'Standard: 0%, Custom: 0%',
                'most_used_boards': 0, 'board_activity_score': 0, 'agile_adoption_rate': 0,
                'board_migration_complexity': 0, 'agile_best_practices_score': 0,
                'board_setup_distribution': 'Simple: 0%, Complex: 0%', 'board_performance_metrics': 'Good: 0%, Needs Improvement: 0%',
                'agile_migration_readiness': 0, 'board_cleanup_requirements': 0
            }

        # 📈 AGILE & BOARDS - 14 REAL KPIs
        # Agile Metrics (7)
        agile_metrics_cards = [
            create_metric_card("Total Rapid Views", kpi_data['total_rapid_views'], "All boards", "#0079BF"),
            create_metric_card("Active Boards", kpi_data['active_boards'], "Currently active", "#61BD4F"),
            create_metric_card("Board Usage", kpi_data['board_usage_by_projects'], "Projects using", "#F2D600"),
            create_metric_card("Agile Projects", kpi_data['agile_projects_count'], "Agile projects", "#FFAB4A"),
            create_metric_card("Board Config", kpi_data['board_configuration_analysis'], "Configuration", "#C377E0"),
            create_metric_card("Most Used Boards", kpi_data['most_used_boards'], "Popular boards", "#00C7E6"),
            # REMOVED: Activity Score - unwanted card
        ]
        
        # Performance & Migration (7)
        performance_migration_cards = [
            create_metric_card("Adoption Rate", f"{kpi_data['agile_adoption_rate']}%", "Agile adoption", "#FF78CB"),
            # REMOVED: Migration Complexity - unwanted card
            create_metric_card("Best Practices", f"{kpi_data['agile_best_practices_score']}/10", "Best practices", "#0079BF"),
            create_metric_card("Setup Distribution", kpi_data['board_setup_distribution'], "Setup complexity", "#61BD4F"),
            create_metric_card("Performance", kpi_data['board_performance_metrics'], "Performance", "#F2D600"),
            create_metric_card("Migration Readiness", f"{kpi_data['agile_migration_readiness']}/10", "Ready for cloud", "#FFAB4A"),
            create_metric_card("Cleanup Required", kpi_data['board_cleanup_requirements'], "Boards to clean", "#EB5A46")
        ]
        
        # Combine all 14 KPI cards
        all_kpi_cards = agile_metrics_cards + performance_migration_cards
        
        # REMOVED: All chart data and chart creation - keeping only cards

        # --- 📈 AGILE & BOARDS LAYOUT (14 KPIs) ---
        return html.Div([
            dbc.Container([
                # Buttons
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="refresh-btn",
                            color="primary",
                            className="me-2"
                        ),
                        dbc.Button(
                            [html.I(className="fas fa-file-download me-2"), html.Span("Generate Report")],
                            id="generate-report-btn",
                            color="success"
                        )
                    ], width="auto"),
                ], className="mb-4", justify="start"),
                

                
                # Agile Metrics Section (7 KPIs)
                html.Div([
                    html.H4("🏃 Agile Metrics", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in agile_metrics_cards[:4]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in agile_metrics_cards[4:]], className="mb-4"),
                ]),
                
                # Performance & Migration Section (7 KPIs)
                html.Div([
                    html.H4("🚀 Performance & Migration", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in performance_migration_cards[:4]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in performance_migration_cards[4:]], className="mb-4"),
                ]),
                
                # REMOVED: Charts Section - keeping only cards as requested
            ], fluid=True)
        ], style={
            "backgroundColor": "#F4F5F7",
            "minHeight": "100vh"
        })

    except Exception as e:
        logger.error(f"Error rendering agile layout: {e}")
        return html.Div([
            dbc.Alert(f"Error loading agile data: {str(e)}", color="danger"),
            html.P("Please check the data connection and try again.")
        ])
