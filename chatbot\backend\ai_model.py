#!/usr/bin/env python3
"""
🤖 JIRA MIGRATION AI MODEL
Free AI assistant using Ollama for Jira Data Center to Cloud migration assistance
"""

import logging
from typing import Dict, List, Optional
import json
import os
import requests

logger = logging.getLogger(__name__)

class JiraMigrationChatbot:
    """
    AI Chatbot specialized in Jira Data Center to Cloud migration guidance
    """
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"  # Default Ollama URL
        self.model_name = "llama3.2"  # Free Llama model
        self.ollama_available = False
        self.knowledge_base = self._load_knowledge_base()
        self.conversation_history = []
        
    def _load_knowledge_base(self) -> Dict:
        """Load Jira migration knowledge base"""
        return {
            "migration_phases": {
                "assessment": "Evaluate current Jira instance, plugins, customizations, and data",
                "planning": "Create migration strategy, timeline, and resource allocation",
                "preparation": "Backup data, test migrations, prepare cloud environment",
                "execution": "Perform actual migration with minimal downtime",
                "validation": "Verify data integrity, functionality, and user acceptance",
                "optimization": "Fine-tune performance and implement cloud-native features"
            },
            "common_challenges": {
                "plugin_compatibility": "Many Data Center plugins may not be available in Cloud",
                "customizations": "Custom workflows, fields, and scripts need review",
                "integrations": "Third-party integrations may require reconfiguration",
                "user_management": "SSO and user provisioning differences",
                "performance": "Cloud performance characteristics differ from on-premise",
                "compliance": "Data residency and security compliance considerations"
            },
            "best_practices": {
                "start_small": "Begin with a pilot project or non-critical instance",
                "clean_data": "Remove unused projects, users, and configurations",
                "test_thoroughly": "Use Atlassian Migration Assistant for testing",
                "train_users": "Provide training on Cloud-specific features",
                "monitor_performance": "Establish baseline metrics before migration",
                "backup_strategy": "Maintain comprehensive backups throughout process"
            }
        }
    
    def initialize_model(self):
        """Initialize Ollama connection"""
        try:
            # Check if Ollama is running
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_available = True
                logger.info(f"✅ Ollama connected successfully at {self.ollama_url}")

                # Check if our model is available
                models = response.json().get('models', [])
                model_names = [model.get('name', '') for model in models]

                if any(self.model_name in name for name in model_names):
                    logger.info(f"✅ Model {self.model_name} is available")
                else:
                    logger.warning(f"⚠️ Model {self.model_name} not found. Available models: {model_names}")
                    # Try to pull the model
                    self._pull_model()

                return True
            else:
                logger.warning("⚠️ Ollama is not responding")
                return False

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Ollama not available: {e}")
            logger.info("💡 To use AI features, install Ollama: https://ollama.ai")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to initialize Ollama: {e}")
            return False

    def _pull_model(self):
        """Pull the model if not available"""
        try:
            logger.info(f"📥 Pulling model {self.model_name}...")
            response = requests.post(
                f"{self.ollama_url}/api/pull",
                json={"name": self.model_name},
                timeout=300  # 5 minutes timeout for model download
            )
            if response.status_code == 200:
                logger.info(f"✅ Model {self.model_name} pulled successfully")
            else:
                logger.error(f"❌ Failed to pull model: {response.text}")
        except Exception as e:
            logger.error(f"❌ Error pulling model: {e}")
    
    def get_response(self, user_message: str, context: Optional[Dict] = None) -> str:
        """
        Generate AI response to user question about Jira migration
        """
        try:
            # First, check if it's a knowledge-base question
            kb_response = self._check_knowledge_base(user_message)
            if kb_response:
                return kb_response
            
            # If Ollama is not available, use fallback responses
            if not self.ollama_available:
                return self._get_fallback_response(user_message)
            
            # Generate AI response using the model
            return self._generate_ai_response(user_message, context)
            
        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return "I apologize, but I'm experiencing technical difficulties. Please try asking your question again."
    
    def _check_knowledge_base(self, message: str) -> Optional[str]:
        """Check if the message matches knowledge base topics"""
        message_lower = message.lower()
        
        # Migration phases
        if any(phase in message_lower for phase in ["phase", "step", "process", "methodology"]):
            phases = self.knowledge_base["migration_phases"]
            response = "🚀 **Jira Migration Phases:**\n\n"
            for i, (phase, description) in enumerate(phases.items(), 1):
                response += f"{i}. **{phase.title()}**: {description}\n"
            return response
        
        # Common challenges
        if any(word in message_lower for word in ["challenge", "problem", "issue", "difficulty"]):
            challenges = self.knowledge_base["common_challenges"]
            response = "⚠️ **Common Migration Challenges:**\n\n"
            for challenge, description in challenges.items():
                response += f"• **{challenge.replace('_', ' ').title()}**: {description}\n"
            return response
        
        # Best practices
        if any(word in message_lower for word in ["best practice", "recommendation", "advice", "tip"]):
            practices = self.knowledge_base["best_practices"]
            response = "✅ **Migration Best Practices:**\n\n"
            for practice, description in practices.items():
                response += f"• **{practice.replace('_', ' ').title()}**: {description}\n"
            return response
        
        return None
    
    def _get_fallback_response(self, message: str) -> str:
        """Provide fallback responses when AI model is not available"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["hello", "hi", "hey", "start"]):
            return "👋 Hello! I'm your Jira Migration Assistant. I can help you with Data Center to Cloud migration questions. What would you like to know?"
        
        if any(word in message_lower for word in ["plugin", "app", "addon"]):
            return "🔌 **Plugin Migration**: Many Data Center plugins have Cloud equivalents, but some may not be available. I recommend using the Atlassian Migration Assistant to check plugin compatibility for your instance."
        
        if any(word in message_lower for word in ["timeline", "duration", "time"]):
            return "⏱️ **Migration Timeline**: Typical migrations take 2-6 months depending on complexity. Factors include data size, customizations, integrations, and testing requirements."
        
        if any(word in message_lower for word in ["cost", "price", "pricing"]):
            return "💰 **Migration Costs**: Consider Cloud licensing, migration services, training, and potential downtime. Use Atlassian's pricing calculator for accurate estimates."
        
        return "🤖 I understand you're asking about Jira migration. Could you be more specific? I can help with migration phases, challenges, best practices, plugins, timelines, and more!"
    
    def _generate_ai_response(self, message: str, context: Optional[Dict] = None) -> str:
        """Generate response using Ollama"""
        try:
            if not self.ollama_available:
                return self._get_fallback_response(message)

            # Create system prompt for Jira migration context
            system_prompt = """You are a Jira migration expert assistant. Help users with Data Center to Cloud migration questions.
            Be concise, practical, and focus on actionable advice. Use emojis sparingly and keep responses under 200 words."""

            # Prepare the prompt
            full_prompt = f"{system_prompt}\n\nUser question: {message}\n\nResponse:"

            # Call Ollama API
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": full_prompt,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "max_tokens": 200
                    }
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result.get('response', '').strip()
                if ai_response:
                    logger.info("✅ Generated AI response using Ollama")
                    return ai_response
                else:
                    logger.warning("⚠️ Empty response from Ollama")
                    return self._get_fallback_response(message)
            else:
                logger.error(f"❌ Ollama API error: {response.status_code}")
                return self._get_fallback_response(message)

        except requests.exceptions.Timeout:
            logger.error("⏱️ Ollama request timeout")
            return self._get_fallback_response(message)
        except Exception as e:
            logger.error(f"❌ AI generation error: {e}")
            return self._get_fallback_response(message)
    
    def add_to_conversation(self, user_message: str, bot_response: str):
        """Add exchange to conversation history"""
        self.conversation_history.append({
            "user": user_message,
            "bot": bot_response,
            "timestamp": logger.info(f"Conversation logged")
        })
        
        # Keep only last 10 exchanges to manage memory
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]

# Global chatbot instance
chatbot_instance = None

def get_chatbot() -> JiraMigrationChatbot:
    """Get or create chatbot instance"""
    global chatbot_instance
    if chatbot_instance is None:
        chatbot_instance = JiraMigrationChatbot()
        # Try to initialize model, but don't fail if it doesn't work
        chatbot_instance.initialize_model()
    return chatbot_instance
