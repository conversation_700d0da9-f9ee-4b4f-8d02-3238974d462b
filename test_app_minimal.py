#!/usr/bin/env python3
"""
MINIMAL APP TEST - TO IDENTIFY WHAT'S BREAKING
"""

import dash
import dash_bootstrap_components as dbc
from dash import html, dcc
from flask import Flask
import logging

print("🔧 STARTING MINIMAL APP TEST...")

# Basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask server
server = Flask(__name__)
print("✅ Flask server created")

# Create Dash app
app = dash.Dash(
    __name__,
    server=server,
    external_stylesheets=[dbc.themes.BOOTSTRAP],
    suppress_callback_exceptions=True
)
print("✅ Dash app created")

# Simple layout
app.layout = html.Div([
    html.H1("🚨 MINIMAL APP TEST 🚨", style={"color": "red", "textAlign": "center"}),
    html.H2("If you see this, the basic app is working!", style={"color": "green", "textAlign": "center"}),
    dbc.<PERSON><PERSON>("Test Button", id="test-btn", color="primary"),
    html.Div(id="test-output")
])
print("✅ Layout created")

# Simple callback
@app.callback(
    dash.dependencies.Output("test-output", "children"),
    [dash.dependencies.Input("test-btn", "n_clicks")]
)
def test_callback(n_clicks):
    if n_clicks:
        return f"Button clicked {n_clicks} times!"
    return "Click the button to test"

print("✅ Callback registered")

if __name__ == "__main__":
    print("🚀 STARTING MINIMAL APP ON PORT 8051...")
    app.run(debug=True, host="127.0.0.1", port=8051)
