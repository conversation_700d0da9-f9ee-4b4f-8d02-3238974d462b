#!/usr/bin/env python3
"""
🎨 CHATBOT FRONTEND LAYOUT
Beautiful chat interface for <PERSON>ra Migration Assistant
"""

from dash import html, dcc
import dash_bootstrap_components as dbc
import logging

logger = logging.getLogger(__name__)

def layout():
    """
    Create the chatbot interface layout
    """
    try:
        logger.info("🤖 Rendering Jira Migration Chatbot layout")
        
        return html.Div([
            dbc.Container([

                
                # Chat Container
                dbc.Row([
                    dbc.Col([
                        # Chat Window
                        dbc.Card([
                            dbc.CardHeader([
                                html.Div([
                                    html.H5("💬 Chat with Migration Expert", className="mb-0"),
                                    dbc.<PERSON>ge("AI Assistant", color="success", className="ms-2")
                                ], className="d-flex align-items-center")
                            ], style={"backgroundColor": "#0079BF", "color": "white"}),
                            
                            dbc.CardBody([
                                # Chat Messages Area
                                html.Div([
                                    # Welcome Message
                                    html.Div([
                                        dbc.<PERSON>ert([
                                            html.Div([
                                                html.I(className="fas fa-robot me-2"),
                                                html.<PERSON>("<PERSON>ra Migration Assistant: "),
                                                html.Span("👋 Hello! I'm here to help with your Jira Data Center to Cloud migration. Ask me about migration phases, challenges, best practices, plugins, timelines, or any other migration-related questions!")
                                            ])
                                        ], color="info", className="mb-3")
                                    ]),
                                    
                                    # Chat Messages Container
                                    html.Div(
                                        id="chat-messages",
                                        children=[],
                                        style={
                                            "height": "400px",
                                            "overflowY": "auto",
                                            "border": "1px solid #dee2e6",
                                            "borderRadius": "0.375rem",
                                            "padding": "1rem",
                                            "backgroundColor": "#f8f9fa"
                                        }
                                    )
                                ], className="mb-3"),
                                
                                # Input Area
                                html.Div([
                                    dbc.InputGroup([
                                        dbc.Input(
                                            id="chat-input",
                                            placeholder="Ask me about Jira migration...",
                                            type="text",
                                            style={"borderRadius": "0.375rem 0 0 0.375rem"}
                                        ),
                                        dbc.Button(
                                            [html.I(className="fas fa-paper-plane me-2"), "Send"],
                                            id="chat-send-btn",
                                            color="primary",
                                            style={"borderRadius": "0 0.375rem 0.375rem 0"}
                                        )
                                    ], className="mb-3"),
                                    
                                    # Quick Action Buttons
                                    html.Div([
                                        html.P("💡 Quick Questions:", className="mb-2 text-muted"),
                                        dbc.ButtonGroup([
                                            dbc.Button("Migration Phases", id="btn-phases", size="sm", outline=True, color="secondary"),
                                            dbc.Button("Common Challenges", id="btn-challenges", size="sm", outline=True, color="secondary"),
                                            dbc.Button("Best Practices", id="btn-practices", size="sm", outline=True, color="secondary"),
                                            dbc.Button("Plugin Migration", id="btn-plugins", size="sm", outline=True, color="secondary")
                                        ], className="flex-wrap")
                                    ])
                                ])
                            ])
                        ], className="chat-card")
                    ], md=8),
                    
                    # Sidebar with Migration Resources
                    dbc.Col([
                        dbc.Card([
                            dbc.CardHeader([
                                html.H5("📚 Migration Resources", className="mb-0")
                            ], style={"backgroundColor": "#61BD4F", "color": "white"}),
                            
                            dbc.CardBody([
                                # Migration Checklist
                                html.Div([
                                    html.H6("✅ Migration Checklist", className="mb-3"),
                                    dbc.ListGroup([
                                        dbc.ListGroupItem([
                                            html.I(className="fas fa-search me-2"),
                                            "Assessment & Planning"
                                        ]),
                                        dbc.ListGroupItem([
                                            html.I(className="fas fa-cogs me-2"),
                                            "Plugin Compatibility"
                                        ]),
                                        dbc.ListGroupItem([
                                            html.I(className="fas fa-database me-2"),
                                            "Data Migration"
                                        ]),
                                        dbc.ListGroupItem([
                                            html.I(className="fas fa-users me-2"),
                                            "User Training"
                                        ]),
                                        dbc.ListGroupItem([
                                            html.I(className="fas fa-check-circle me-2"),
                                            "Go-Live & Validation"
                                        ])
                                    ], flush=True, className="mb-4")
                                ]),
                                
                                # Useful Links
                                html.Div([
                                    html.H6("🔗 Useful Links", className="mb-3"),
                                    dbc.ListGroup([
                                        dbc.ListGroupItem([
                                            html.A([
                                                html.I(className="fas fa-external-link-alt me-2"),
                                                "Migration Assistant"
                                            ], href="https://www.atlassian.com/migration/cloud", target="_blank", className="text-decoration-none")
                                        ]),
                                        dbc.ListGroupItem([
                                            html.A([
                                                html.I(className="fas fa-external-link-alt me-2"),
                                                "Cloud Pricing"
                                            ], href="https://www.atlassian.com/software/jira/pricing", target="_blank", className="text-decoration-none")
                                        ]),
                                        dbc.ListGroupItem([
                                            html.A([
                                                html.I(className="fas fa-external-link-alt me-2"),
                                                "Migration Guide"
                                            ], href="https://www.atlassian.com/migration/cloud/guide", target="_blank", className="text-decoration-none")
                                        ])
                                    ], flush=True)
                                ])
                            ])
                        ], className="resource-card")
                    ], md=4)
                ])
                
            ], fluid=True),
            
            # Hidden div to store conversation state
            html.Div(id="conversation-state", style={"display": "none"})
            
        ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh", "padding": "2rem 0"})
        
    except Exception as e:
        logger.error(f"Error in chatbot layout: {e}")
        return html.Div([
            dbc.Alert(f"Error: {str(e)}", color="danger"),
            html.P("Chatbot layout failed to load.")
        ])
