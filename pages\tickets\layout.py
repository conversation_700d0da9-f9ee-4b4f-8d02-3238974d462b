from dash import html, dcc
import dash_bootstrap_components as dbc
import pandas as pd
import logging
from components.cards import create_metric_card
from kpi_calculator import get_issues_content_kpis

logger = logging.getLogger(__name__)

def layout():
    try:
        logger.info("📋 Rendering Issues & Content Analysis - CARDS ONLY (NO CHARTS)")

        # Get real KPI data from load schema
        try:
            kpi_data = get_issues_content_kpis()
            logger.info(f"✅ Loaded {len(kpi_data)} Issues & Content KPIs")
        except Exception as e:
            logger.error(f"❌ Error loading Issues KPIs: {e}")
            # Fallback to default values
            kpi_data = {
                'total_issues': 0, 'open_issues': 0, 'resolved_issues': 0, 'priority_distribution': 'High: 0%, Medium: 0%, Low: 0%',
                'issue_status_staleness': 0, 'resolution_completion_rate': 0, 'issues_with_attachments': 0,
                'issues_with_comments': 0, 'issues_with_worklogs': 0, 'avg_issue_lifecycle_days': 0,
                'content_migration_size_gb': 0, 'large_issues_count': 0, 'issue_complexity_score': 0,
                'content_cleanup_required': 0, 'issue_migration_readiness': 0, 'data_quality_score': 0
            }

        # 📋 ISSUES & CONTENT ANALYSIS - 16 REAL KPIs
        # Issue Metrics (8)
        issue_metrics_cards = [
            create_metric_card("Total Issues", kpi_data['total_issues'], "All issues", "#0079BF"),
            create_metric_card("Open Issues", kpi_data['open_issues'], "Currently open", "#F2D600"),
            create_metric_card("Resolved Issues", kpi_data['resolved_issues'], "Completed", "#61BD4F"),
            create_metric_card("Priority Distribution", kpi_data['priority_distribution'], "Priority spread", "#FFAB4A"),
            create_metric_card("Status Staleness", kpi_data['issue_status_staleness'], "Stale issues", "#EB5A46"),
            create_metric_card("Completion Rate", f"{kpi_data['resolution_completion_rate']}%", "Resolution rate", "#C377E0"),
            create_metric_card("Issues w/ Attachments", kpi_data['issues_with_attachments'], "Have files", "#00C7E6"),
            create_metric_card("Issues w/ Comments", kpi_data['issues_with_comments'], "Have comments", "#51E898")
        ]

        # Content & Migration (8)
        content_migration_cards = [
            create_metric_card("Issues w/ Worklogs", kpi_data['issues_with_worklogs'], "Time tracked", "#FF78CB"),
            create_metric_card("Avg Lifecycle", f"{kpi_data['avg_issue_lifecycle_days']} days", "Resolution time", "#4ECDC4"),
            create_metric_card("Content Size", f"{kpi_data['content_migration_size_gb']} GB", "Migration size", "#0079BF"),
            create_metric_card("Large Issues", kpi_data['large_issues_count'], "Complex issues", "#61BD4F"),
            create_metric_card("Complexity Score", f"{kpi_data['issue_complexity_score']}/10", "Issue complexity", "#F2D600"),
            create_metric_card("Cleanup Required", kpi_data['content_cleanup_required'], "Need cleanup", "#EB5A46"),
            create_metric_card("Migration Readiness", f"{kpi_data['issue_migration_readiness']}/10", "Ready for cloud", "#FFAB4A"),
            create_metric_card("Data Quality", f"{kpi_data['data_quality_score']}/10", "Data quality", "#C377E0")
        ]

        # Combine all 16 KPI cards
        all_kpi_cards = issue_metrics_cards + content_migration_cards

        # REMOVED: All chart data and chart creation - keeping only cards

        return html.Div([
            dbc.Container([


                # Refresh button
                dbc.Row([
                    dbc.Col([
                        dbc.Button(
                            [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                            id="tickets-refresh",
                            color="primary",
                            className="mb-4"
                        )
                    ], width="auto"),
                ]),

                # Issue Metrics Section (8 KPIs)
                html.Div([
                    html.H4("📊 Issue Metrics", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in issue_metrics_cards[:4]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in issue_metrics_cards[4:]], className="mb-4"),
                ]),

                # Content & Migration Section (8 KPIs)
                html.Div([
                    html.H4("📦 Content & Migration", className="section-title mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in content_migration_cards[:4]], className="mb-3"),
                    dbc.Row([dbc.Col(card, className="mb-4 kpi-card-col") for card in content_migration_cards[4:]], className="mb-4"),
                ]),

                # REMOVED: Charts Section - keeping only cards as requested
            ], fluid=True)
        ], style={
            "backgroundColor": "#F4F5F7",
            "minHeight": "100vh"
        })

    except Exception as e:
        logger.error(f"Error rendering tickets layout: {e}")
        return html.Div([
            dbc.Alert(f"Error loading ticket data: {str(e)}", color="danger"),
            html.P("Please check the data connection and try again.")
        ])