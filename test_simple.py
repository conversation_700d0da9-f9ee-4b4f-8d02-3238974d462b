#!/usr/bin/env python3
"""
SIMPLE TEST TO CHECK PYTHON ENVIRONMENT
"""

print("🔧 Testing Python environment...")
print("✅ Python is working!")

try:
    import dash
    print("✅ Dash imported successfully")
except Exception as e:
    print(f"❌ Dash import failed: {e}")

try:
    import dash_bootstrap_components as dbc
    print("✅ DBC imported successfully")
except Exception as e:
    print(f"❌ DBC import failed: {e}")

try:
    from pages.home.layout import layout as home_layout
    print("✅ Home layout imported successfully")
except Exception as e:
    print(f"❌ Home layout import failed: {e}")

print("🎯 Test completed!")
