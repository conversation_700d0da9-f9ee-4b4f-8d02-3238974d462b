from dash import html, dcc
import dash_bootstrap_components as dbc
import pandas as pd
import logging
from components.cards import create_metric_card
from components.tables import create_data_table
from components.charts import create_bar_chart, create_pie_chart

logger = logging.getLogger(__name__)

def check_etl_tables_exist(analyzer):
    """Check if key ETL tables exist in the load schema"""
    try:
        # Try to query a few key tables to see if they exist
        test_queries = [
            "SELECT COUNT(*) FROM load.dim_project LIMIT 1",
            "SELECT COUNT(*) FROM load.fact_jiraissue LIMIT 1",
            "SELECT COUNT(*) FROM load.dim_cwd_user LIMIT 1"
        ]

        for query in test_queries:
            try:
                result = analyzer.execute_query(query)
                # If we get here, the table exists
            except Exception as e:
                if "does not exist" in str(e):
                    logger.info(f"ETL table check: Tables don't exist yet - {e}")
                    return False
                else:
                    # Some other error, but table might exist
                    logger.warning(f"ETL table check warning: {e}")

        logger.info("✅ ETL tables exist - data loading can proceed")
        return True

    except Exception as e:
        logger.error(f"❌ Error checking ETL tables: {e}")
        return False

def layout():
    """
    Home layout - data loaded via callbacks to prevent connection storms
    """
    logger.info("🏠 Rendering Home page layout (data loaded via callbacks)")

    # Always show the ETL buttons and content containers
    return html.Div([
        dbc.Container([
            # Buttons
            dbc.Row([
                dbc.Col([
                    dbc.Button(
                        [html.I(className="fas fa-sync-alt me-2"), html.Span("Refresh Data")],
                        id="home-refresh",
                        color="primary",
                        className="me-2"
                    ),
                    dbc.Button(
                        [html.I(className="fas fa-database me-2"), html.Span("Run ETL")],
                        id="run-etl-btn",
                        color="warning",
                        className="me-2"
                    ),
                    dbc.Button(
                        [html.I(className="fas fa-file-download me-2"), html.Span("Generate Report")],
                        id="generate-report-btn",
                        color="success"
                    )
                ], width="auto"),
            ], className="mb-4", justify="between"),

            # ETL Status Alert
            html.Div(id="etl-status", className="mb-3"),

            # Hidden download component
            dcc.Download(id="download-report"),



            # Content containers - populated by callbacks
            html.Div(id="metric-cards", className="mb-4"),
            html.Div(id="instance-table", className="mb-4"),
            html.Div(id="home-metrics", className="mb-4"),

        ], fluid=True)
    ], style={"backgroundColor": "#F4F5F7", "minHeight": "100vh"})